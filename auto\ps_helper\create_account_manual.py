from auto.ps_helper.mail_util import add_user
from auto.ps_helper.create_identity import IdentityGen
from auto.ps_helper.get_email_hyperlink import get_link
from auto.ps_helper.postgres import PostgrestClient
from pprint import pprint
import random
import threading
import re
import pyperclip
import csv
import os
import json
import time
from pathlib import Path
from typing import Dict, Optional, Any
from camoufox import Async<PERSON><PERSON>uf<PERSON>, launch_options
from auto.camoufox.proxy_manager import get_proxy
import sys
import asyncio

if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

def opts_path(name: str) -> str:
    return f"auto/camoufox/profiles/{name}/opts.json"

def config_exists(name: str) -> bool:
    return Path(opts_path(name)).exists()

def generate_profile(name: str, proxy: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
    """Generate a new profile configuration.

    Args:
        name: Profile name
        proxy: Optional proxy configuration

    Returns:
        New profile configuration dictionary
    """
    profile_dir = os.path.dirname(opts_path(name))

    try:
        opts = launch_options(
            user_data_dir=profile_dir,
            os=['windows'],
            proxy=proxy,
            geoip=True,
            window=(1080, 720),
            humanize=True,
            headless=False
        )

        os.makedirs(profile_dir, exist_ok=True)
        with open(opts_path(name), "w") as opts_file:
            json.dump(opts, opts_file, indent=4)
        return opts

    except Exception as e:
        print(f"Error generating profile: {e}")
        raise

def get_profile(name: str, proxy: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
    """Get or create a profile configuration.

    Args:
        name: Profile name
        proxy: Optional proxy configuration

    Returns:
        Profile configuration dictionary
    """
    if config_exists(name=name):
        with open(opts_path(name)) as opts_file:
            return json.load(opts_file)
    return generate_profile(name, proxy)

async def generate_identity():
    identity = IdentityGen()
    add_user(identity['email'])
    return identity

async def get_verification_email(email):
    link = await get_link(email)
    pyperclip.copy(link)
    return link

async def start_browser():
    proxy = get_proxy()
    
    # Generate a unique profile name
    profile_name = f"ps_account_{int(time.time())}"
    
    # Get profile options with proxy
    profile_opts = get_profile(profile_name, proxy)
    
    # Launch CamouFox browser
    try:
        browser = await AsyncCamoufox(from_options=profile_opts, persistent_context=True, headless=False).__aenter__()
        page = await browser.new_page()
        await page.goto("https://playstation.com/es-mx/")
        return browser, page
    except Exception as e:
        print(f"Error launching browser: {e}")
        raise


async def close_browser(browser_data):
    browser, page = browser_data
    await browser.close()

async def submit_sso(identity:dict):
    sso = await async_input("\nEnter sso: ")
    if sso:
        match = re.search(r'"npsso":"([^"]+)"', sso)
        if match:
            npsso_value = match.group(1)
            postgres = PostgrestClient()
            response = await postgres.register_identity(identity)
            print(response)
            await postgres.register_identity_sso(response['id'], npsso_value)


async def copy_handler(info_required, identity):
    print(identity[info_required])
    pyperclip.copy(identity[info_required])

async def async_input(prompt):
    return await asyncio.get_event_loop().run_in_executor(None, input, prompt)

async def show_menu(options, title=None):
    print()
    if title:
        print(title)
    for i, option in enumerate(options, 1):
        print(f"{i}: {option}")
    
    while True:
        try:
            choice = int(await async_input("\nEnter choice: "))
            if 1 <= choice <= len(options):
                return options[choice-1]
            print(f"Please enter 1-{len(options)}")
        except ValueError:
            print("Please enter a number")

async def run():
    browser_data = None
    identity = None
    finished = False
    while not finished:
        action = await show_menu(["Start Browser", "Close Browser", "Generate Identity", "Get Verification Link", "Get SSO Code",
                                   "Submit SSO Code", "Copy DoB", "Copy City", "Copy Postcode", "Copy Email", "Copy Username",
                                    "Copy Password", "Copy Firstname", "Copy Lastname", "End Program"])
        if action == "Start Browser":
            if browser_data is None:
                browser_data = await start_browser()
                print("CamouFox browser started.")
            else:
                print("Browser is already running.")
        if action == "Close Browser":
            if browser_data:
                await close_browser(browser_data)
                browser_data = None
                print("Browser closed.")
            else:
                print("No Browser to close.")
        if action == "Generate Identity":
            identity = await generate_identity()
            print(identity)
        if action == "Get Verification Link":
            if identity != None:
                await get_verification_email(identity['email'])
            else:
                print("Haven't Created An Identity Yet")
        if action == "Get SSO Code":
            print("https://ca.account.sony.com/api/v1/ssocookie")
            pyperclip.copy("https://ca.account.sony.com/api/v1/ssocookie")
        if action == "Submit SSO Code":
            await submit_sso(identity)
        if re.match(r'Copy', action):
            if identity != None:
                await copy_handler(" ".join(action.split(" ")[1:]).lower(), identity)
        if action == "End Program":
            finished = True
            if browser_data != None:
                await close_browser(browser_data)

if __name__ == "__main__":
    import asyncio
    asyncio.run(run())


